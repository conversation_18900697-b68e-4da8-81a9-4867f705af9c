# Architecture Documentation

## Project Overview

Twitchy TV Time is a TV clock application that displays an analog clock alongside Swedish TV schedule information. The application is built with vanilla JavaScript and Vite, designed to be simple, responsive, and customizable.

## Core Features

- **Analog Clock**: SVG-based clock with smooth animations
- **TV Schedule Integration**: Real-time TV show information from SVT2
- **Responsive Design**: 4:3 aspect ratio layout that adapts to different screen sizes
- **Multiple Clock Views**: Different visual themes and data sources (SVT2-1980, SVT2-1970, etc.)
- **URL-based Navigation**: Clock type is reflected in the URL for bookmarking/sharing

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                        Frontend (Vanilla JS)                │
├─────────────────────────────────────────────────────────────┤
│  Clock Component  │  TV Schedule  │  Theme Manager  │  Router │
│  - SVG rendering  │  - Data fetch │  - Asset load   │  - URL  │
│  - Time updates   │  - Display    │  - CSS vars     │  - Nav  │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Cloudflare Pages Functions               │
├─────────────────────────────────────────────────────────────┤
│  /tvschedule      │  Future: /tvschedule-1970              │
│  - SVT2 scraping  │  - Alternative data sources            │
│  - Data caching   │  - Type-specific endpoints              │
└─────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
twitchy-tv-time/
├── functions/                  # Cloudflare Pages Functions
│   └── tvschedule.js          # TV schedule scraper for SVT2
├── public/                    # Static assets
│   ├── TV2.svg               # SVT2 logo
│   ├── icons/                # PWA icons
│   └── fonts/                # Custom fonts
├── src/
│   ├── assets/               # SVG assets organized by clock type
│   │   ├── svt2-1980/       # SVT2-1980 theme assets
│   │   │   ├── hour-hand.svg
│   │   │   ├── minute-hand.svg
│   │   │   ├── second-hand.svg
│   │   │   └── hour-markers.svg
│   │   └── svt2-1970/       # SVT2-1970 theme assets (future)
│   │       ├── hour-hand.svg
│   │       ├── minute-hand.svg
│   │       ├── second-hand.svg
│   │       └── hour-markers.svg
│   ├── components/           # Modular components (planned)
│   │   ├── clock.js         # Clock logic
│   │   ├── tv-schedule.js   # TV schedule logic
│   │   └── theme-manager.js # Theme switching logic
│   ├── styles/
│   │   ├── global.css       # Base styles and CSS variables
│   │   └── themes/          # Theme-specific styles (planned)
│   │       ├── svt2-1980.css
│   │       └── svt2-1970.css
│   └── main.js              # Application entry point
├── test/                     # Test files
│   ├── functions/           # Function tests
│   └── components/          # Component tests (planned)
├── index.html               # Main HTML template
├── vite.config.js          # Vite configuration
└── package.json            # Dependencies and scripts
```

## Component Architecture

### Clock Component
- **Responsibility**: Renders and updates the analog clock
- **Key Features**:
  - SVG-based rendering for scalability
  - Real-time updates with smooth animations
  - Responsive sizing
  - Theme-aware asset loading

### TV Schedule Component
- **Responsibility**: Fetches and displays TV show information
- **Key Features**:
  - Caches data for 24 hours
  - Updates display every minute
  - Handles different data sources per clock type
  - Error handling with fallback content

### Theme Manager
- **Responsibility**: Manages clock types and visual themes
- **Key Features**:
  - Dynamic asset loading
  - CSS variable updates
  - URL synchronization
  - Theme persistence

### Router
- **Responsibility**: Handles URL-based navigation
- **Key Features**:
  - Hash-based routing (`#svt2-1980`)
  - Theme switching via URL
  - Browser history support

## Clock Types System

### Current Implementation
- **SVT2-1980**: Default theme with green color scheme and SVT2 data
- **SVT2-1970**: Retro theme with brown/beige color scheme (placeholder implementation)

### Implementation Status
- ✅ URL-based theme switching (`#svt2-1980`, `#svt2-1970`)
- ✅ Dynamic asset loading per theme
- ✅ CSS variable updates for color themes
- ✅ Theme switch button in UI
- ✅ Browser history support (back/forward navigation)
- ⏳ Different TV schedule endpoints per theme (planned)

### Clock Type Properties
Each clock type defines:
1. **Visual Assets**: SVG files for hands and markers
2. **Color Theme**: CSS variables for colors
3. **Data Source**: TV schedule endpoint URL
4. **Logo/Branding**: Channel-specific imagery

## Data Flow

### Clock Updates
1. Initialize clock on page load
2. Sync with real-time seconds
3. Update hands every second
4. Trigger TV schedule updates on minute changes

### TV Schedule Updates
1. Check cache validity (24-hour expiry)
2. Fetch from appropriate endpoint based on clock type
3. Parse and display next show information
4. Handle errors gracefully with fallback content

### Theme Switching
1. User clicks theme switch button
2. Update URL hash
3. Load new theme assets
4. Update CSS variables
5. Reinitialize components if needed

## Technology Stack

- **Frontend**: Vanilla JavaScript (ES6+)
- **Build Tool**: Vite
- **Styling**: CSS with custom properties
- **Backend**: Cloudflare Pages Functions
- **Testing**: Vitest with MSW for API mocking
- **Deployment**: Cloudflare Pages

## Performance Considerations

- **Asset Loading**: SVG assets are loaded dynamically per theme
- **Caching**: TV schedule data cached for 24 hours
- **Animations**: CSS transforms for smooth clock hand movement
- **Responsive**: Single layout adapts to all screen sizes

## Future Enhancements

1. **Additional Clock Types**: More historical TV themes
2. **Gesture Support**: Swipe navigation between themes
3. **Settings Panel**: User customization options
4. **Offline Support**: Service worker for PWA functionality
5. **Multiple Channels**: Support for other Swedish TV channels
