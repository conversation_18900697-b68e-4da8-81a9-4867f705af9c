import { describe, it, expect, vi } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '../setup.js';
import { onRequest, extractSVT2Schedule } from '../../functions/tvschedule.js';

describe('TV Schedule Functions', () => {
  describe('extractSVT2Schedule', () => {
    it('extracts shows from valid HTML', () => {
      const mockHtml = `
        <div id="svt2-svt-se">
          <a href="/tabla/svt2/p/show1/202505181900/"><time>19:00</time>NEWS</a>
          <a href="/tabla/svt2/p/show2/202505182000/"><time>20:00</time>DOCUMENTARY</a>
        </div>
      `;

      const result = extractSVT2Schedule(mockHtml);
      
      expect(result.shows).toHaveLength(2);
      expect(result.shows[0]).toEqual({
        time: '19:00',
        title: 'NEWS',
        dateTime: '202505181900'
      });
      expect(result.totalShows).toBe(2);
    });

    it('handles HTML without SVT2 section', () => {
      const result = extractSVT2Schedule('<div>Some other content</div>');
      
      expect(result.shows).toHaveLength(0);
      expect(result.error).toBe('Could not find SVT2 section on the page');
    });
  });

  describe('onRequest', () => {
    it('returns schedule data on successful fetch', async () => {
      const mockHtml = `
        <div id="svt2-svt-se">
          <a href="/tabla/svt2/p/show1/202505181900/"><time>19:00</time>NEWS</a>
        </div>
      `;

      server.use(
        http.get('https://tv-tabla.se/', () => {
          return HttpResponse.text(mockHtml);
        })
      );

      const context = {
        request: new Request('https://example.com')
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.shows).toHaveLength(1);
      expect(data.shows[0].title).toBe('NEWS');
    });

    it('handles fetch errors', async () => {
      server.use(
        http.get('https://tv-tabla.se/', () => {
          return new HttpResponse(null, { status: 500 });
        })
      );

      const context = {
        request: new Request('https://example.com')
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to fetch TV schedule');
    });

    it('handles CORS preflight requests', async () => {
      const context = {
        request: new Request('https://example.com', { method: 'OPTIONS' })
      };

      const response = await onRequest(context);

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('GET, OPTIONS');
    });
  });
});