import { describe, it, expect, vi } from 'vitest';
import { http, HttpResponse } from 'msw';
import { server } from '../setup.js';
import { onRequest } from '../../functions/tvschedule/[channel].js';

describe('Dynamic TV Schedule Functions', () => {
  describe('Channel-specific endpoints', () => {
    it('should handle SVT2 channel requests', async () => {
      // Mock the tv-tabla.se response
      server.use(
        http.get('https://tv-tabla.se/', () => {
          return HttpResponse.text(`
            <div id="svt2-svt-se">
              <a href="/tabla/svt2/p/show1/202505181900/"><time>19:00</time>NEWS</a>
              <a href="/tabla/svt2/p/show2/202505182000/"><time>20:00</time>DOCUMENTARY</a>
            </div>
          `);
        })
      );

      // Create mock context for SVT2
      const context = {
        params: { channel: 'svt2' },
        request: { method: 'GET' }
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.channel).toBe('SVT2');
      expect(data.shows).toHaveLength(2);
      expect(data.shows[0]).toEqual({
        time: '19:00',
        title: 'NEWS',
        dateTime: '202505181900'
      });
    });

    it('should handle SVT1 channel requests', async () => {
      // Mock the tv-tabla.se response
      server.use(
        http.get('https://tv-tabla.se/', () => {
          return HttpResponse.text(`
            <div id="svt1-svt-se">
              <a href="/tabla/svt1/p/show1/202505181900/"><time>19:00</time>EVENING NEWS</a>
            </div>
          `);
        })
      );

      // Create mock context for SVT1
      const context = {
        params: { channel: 'svt1' },
        request: { method: 'GET' }
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.channel).toBe('SVT1');
      expect(data.shows).toHaveLength(1);
      expect(data.shows[0]).toEqual({
        time: '19:00',
        title: 'EVENING NEWS',
        dateTime: '202505181900'
      });
    });

    it('should return 400 for invalid channel', async () => {
      // Create mock context for invalid channel
      const context = {
        params: { channel: 'invalid' },
        request: { method: 'GET' }
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid channel');
      expect(data.message).toContain('Channel \'invalid\' not supported');
    });

    it('should handle CORS OPTIONS requests', async () => {
      // Create mock context for OPTIONS request
      const context = {
        params: { channel: 'svt2' },
        request: { method: 'OPTIONS' }
      };

      const response = await onRequest(context);

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toBe('GET, OPTIONS');
    });

    it('should handle network errors gracefully', async () => {
      // Mock network error
      server.use(
        http.get('https://tv-tabla.se/', () => {
          return HttpResponse.error();
        })
      );

      // Create mock context
      const context = {
        params: { channel: 'svt2' },
        request: { method: 'GET' }
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toBe('Failed to fetch TV schedule');
      expect(data.channel).toBe('svt2');
    });

    it('should return empty shows when channel container not found', async () => {
      // Mock response without the expected channel container
      server.use(
        http.get('https://tv-tabla.se/', () => {
          return HttpResponse.text('<div>No channel data</div>');
        })
      );

      // Create mock context
      const context = {
        params: { channel: 'svt2' },
        request: { method: 'GET' }
      };

      const response = await onRequest(context);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.channel).toBe('SVT2');
      expect(data.shows).toHaveLength(0);
      expect(data.totalShows).toBe(0);
    });
  });
});
