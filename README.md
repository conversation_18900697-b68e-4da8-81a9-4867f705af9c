# SVT Clock

A TV clock application with SVT2 schedule information, built with vanilla JavaScript.

## Features

- Clean, elegant analog clock design
- Responsive layout that fills the entire window
- Dynamic resizing when the browser window changes
- Smooth animations for clock hands
- No framework dependencies
- SVT2 TV schedule integration via Cloudflare Pages Functions

## Project Structure

The project follows a simple, focused structure:

```
functions/           # Cloudflare Pages Functions
└── tvschedule.js    # TV schedule scraper for SVT2
public/              # Static assets
└── TV2.svg          # SVT2 logo
src/
├── assets/          # SVG assets for clock
│   ├── hour-hand.svg    # SVG for hour hand
│   ├── minute-hand.svg  # SVG for minute hand
│   ├── second-hand.svg  # SVG for second hand
│   └── hour-markers.svg # SVG for hour markers
├── main.js          # Main application entry point
└── styles/          # Global styles
```

## Technologies Used

- JavaScript
- CSS with custom properties (variables)
- Vite for fast development and building
- Cloudflare Pages Functions for serverless API endpoints

## How It Works

The clock uses:
- Object-oriented JavaScript for clean, maintainable code
- Window resize event listeners to dynamically adjust the clock size
- External SVG files for clock hands, making customization easier
- SVG for rendering the clock face and hands
- CSS transitions for smooth animations
- Responsive design principles to fill the available space

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```sh
git clone https://github.com/yourusername/simple-clock.git
cd simple-clock
```

2. Install dependencies:
```sh
npm install
```

3. Start the development server:
```sh
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Available Scripts

- `npm run dev` - Start the development server
- `npm run build` - Build the production version
- `npm run preview` - Preview the production build locally
- `npm run lint` - Run ESLint to check for code issues
- `npm run lint:fix` - Fix ESLint issues automatically

## Customization

### CSS Styling

You can easily customize the clock by modifying the CSS variables in `src/styles/global.css`:

```css
:root {
  --primary-color: #085E20;  /* Background color */
  --text-color: white;       /* Text color */
  --clock-color: white;      /* Clock face color */
  --hand-color: black;       /* Clock hands color */
}
```

### Clock Hands

The clock hands are defined as SVG files in the `src/assets` directory:
- `hour-hand.svg` - The hour hand
- `minute-hand.svg` - The minute hand
- `second-hand.svg` - The second hand
- `hour-markers.svg` - The hour markers

You can modify these SVG files to change the appearance of the clock hands. The SVGs use `currentColor` for fill and stroke, which means they will automatically use the `--hand-color` CSS variable.

## API Endpoints

### `/tvschedule`

Fetches the current TV schedule for SVT2 from tv-tabla.se.

**Method:** GET

**Parameters:**
None

**Response:**

```json
{
  "channel": "SVT2",
  "date": "YYYY-MM-DD",
  "shows": [
    {
      "time": "HH:MM",
      "dateTime": "YYYYMMDDHHMM"
    },
    ...
  ],
  "currentShow": {
    "time": "HH:MM",
    "dateTime": "YYYYMMDDHHMM"
  },
  "nextShow": {
    "time": "HH:MM",
    "dateTime": "YYYYMMDDHHMM"
  },
  "lastUpdated": "ISO date string",
  "totalShows": 30
}
```
