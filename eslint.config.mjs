import { defineConfig } from 'eslint/config';
import js from '@eslint/js';
import globals from 'globals';

export default defineConfig([
  // Base configurations for all files
  {
    linterOptions: {
      reportUnusedDisableDirectives: true,
    },
  },

  // JavaScript files configuration
  {
    files: ['src/*.{js,mjs,cjs}', 'functions/*.{js,mjs,cjs}'],
    plugins: { js },
    extends: ['js/recommended']
  },
  {
    files:  ['src/*.{js,mjs,cjs}', 'functions/*.{js,mjs,cjs}'],
    languageOptions: { globals: globals.browser },
    rules: {
      // Basic style rules
      'indent': ['error', 2],
      'semi': ['error', 'always'],
      'quotes': ['error', 'single'],
      'no-unused-vars': ['warn'],

      // Spacing before function parenthesis
      'space-before-function-paren': ['error', {
        'anonymous': 'always',
        'named': 'never',
        'asyncArrow': 'always'
      }]
    }
  }
]);