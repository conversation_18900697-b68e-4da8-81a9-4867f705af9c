{"name": "twitchy-tv-time", "version": "1.0.0", "type": "module", "description": "A TV clock application built with Vite.", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage"}, "dependencies": {"jsdom": "^26.1.0", "vite": "^6.2.5"}, "devDependencies": {"@eslint/js": "^9.24.0", "@vitest/browser": "^3.1.3", "eslint": "^9.24.0", "globals": "^16.0.0", "msw": "^2.8.3", "vite-plugin-html": "^3.0.0", "vitest": "^3.1.3", "wrangler": "^4.15.2"}}