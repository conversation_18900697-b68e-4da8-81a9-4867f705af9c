// Import styles and SVG assets
import './styles/global.css';

// Clock type configuration
const CLOCK_TYPES = {
  'svt2-1980': {
    name: 'SVT2 1980',
    assets: {
      hourHand: '/src/assets/svt2-1980/hour-hand.svg',
      minuteHand: '/src/assets/svt2-1980/minute-hand.svg',
      secondHand: '/src/assets/svt2-1980/second-hand.svg',
      hourMarkers: '/src/assets/svt2-1980/hour-markers.svg'
    },
    logo: '/TV2.svg',
    colors: {
      primary: '#085E20',
      text: 'white',
      clock: 'white',
      hand: '#085E20'
    },
    tvScheduleUrl: '/tvschedule/svt2'
  },
  'svt2-1970': {
    name: 'SVT2 1970',
    assets: {
      hourHand: '/src/assets/svt2-1970/hour-hand.svg',
      minuteHand: '/src/assets/svt2-1970/minute-hand.svg',
      secondHand: '/src/assets/svt2-1970/second-hand.svg',
      hourMarkers: '/src/assets/svt2-1970/hour-markers.svg'
    },
    logo: '/TV2.svg', // Will be different logo in the future
    colors: {
      primary: '#8B4513',
      text: 'white',
      clock: '#F5F5DC',
      hand: '#8B4513'
    },
    tvScheduleUrl: '/tvschedule/svt1' // Different channel for 1970s
  }
};

// Current clock type
let currentClockType = 'svt2-1980';

// Global variable to store the TV schedule data
let cachedScheduleData = null;
let lastFetchTime = 0;
const ONE_DAY_MS = 24 * 60 * 60 * 1000;

// Theme management functions
const getCurrentClockType = () => {
  const hash = window.location.hash.slice(1); // Remove the #
  return CLOCK_TYPES[hash] ? hash : 'svt2-1980';
};

const setClockType = (type) => {
  if (!CLOCK_TYPES[type]) {
    console.error(`Unknown clock type: ${type}`);
    return;
  }

  currentClockType = type;
  window.location.hash = type;
  applyTheme(type);

  // Clear cached data when switching types to force refresh
  cachedScheduleData = null;
  lastFetchTime = 0;

  // Update the clock assets
  updateClockAssets(type);

  // Update TV schedule
  updateTVSchedule();
};

const applyTheme = (type) => {
  const theme = CLOCK_TYPES[type];
  const root = document.documentElement;

  // Update CSS variables
  root.style.setProperty('--primary-color', theme.colors.primary);
  root.style.setProperty('--text-color', theme.colors.text);
  root.style.setProperty('--clock-color', theme.colors.clock);
  root.style.setProperty('--hand-color', theme.colors.hand);

  // Update logo
  const logoElement = document.getElementById('channel-logo');
  if (logoElement) {
    logoElement.src = theme.logo;
  }
};

const updateClockAssets = (type) => {
  const theme = CLOCK_TYPES[type];

  // Update SVG asset references
  const hourMarkersGroup = document.querySelector('.hour-markers-group use');
  const hourHandGroup = document.querySelector('.hour-hand-group use');
  const minuteHandGroup = document.querySelector('.minute-hand-group use');
  const secondHandGroup = document.querySelector('.second-hand-group use');

  if (hourMarkersGroup) hourMarkersGroup.setAttribute('xlink:href', `${theme.assets.hourMarkers}#hour-markers`);
  if (hourHandGroup) hourHandGroup.setAttribute('xlink:href', `${theme.assets.hourHand}#hour-hand`);
  if (minuteHandGroup) minuteHandGroup.setAttribute('xlink:href', `${theme.assets.minuteHand}#minute-hand`);
  if (secondHandGroup) secondHandGroup.setAttribute('xlink:href', `${theme.assets.secondHand}#second-hand`);
};

// Function to fetch TV schedule data
const fetchTVSchedule = async () => {
  const now = Date.now();
  const theme = CLOCK_TYPES[currentClockType];

  // Only fetch new data if we don't have cached data or it's been more than a day
  if (!cachedScheduleData || now - lastFetchTime > ONE_DAY_MS) {
    try {
      console.log('Fetching TV schedule...');
      const response = await fetch(theme.tvScheduleUrl);
      console.log('Response received:', response.status);
      if (!response.ok) {
        throw new Error(`Failed to fetch TV schedule: ${response.status}`);
      }
      const data = await response.json();
      console.log('TV schedule data:', data);

      // Update the cache and last fetch time
      cachedScheduleData = data;
      lastFetchTime = now;

      return data;
    } catch (error) {
      console.error('Error fetching TV schedule:', error);
      return cachedScheduleData; // Return cached data even on error
    }
  }

  return cachedScheduleData;
};

const parseDateTime = (dateTimeStr) => {
  return new Date(
    dateTimeStr.slice(0, 4),    // year
    dateTimeStr.slice(4, 6) - 1,  // month (0-based)
    dateTimeStr.slice(6, 8),    // day
    dateTimeStr.slice(8, 10),   // hours
    dateTimeStr.slice(10, 12)   // minutes
  ).getTime();
};

// Function to find the next show based on current time
const findNextShow = (shows) => {
  if (!shows || !Array.isArray(shows) || shows.length === 0) {
    return null;
  }

  const now = Date.now();

  // Find the next show (first show that hasn't started yet)
  for (let i = 0; i < shows.length; i++) {
    const showDateTime = parseDateTime(shows[i].dateTime);
    if (showDateTime > now) {
      return shows[i];
    }
  }

  // If all shows have passed, return the first show (assuming it's for tomorrow)
  console.log('All shows have passed, returning first show for tomorrow');
  return shows[0];
};

// Function to update the TV schedule display
const updateTVSchedule = async () => {
  const bottomContainer = document.querySelector('.bottom-container');

  try {
    // Fetch the TV schedule if needed
    const scheduleData = await fetchTVSchedule();

    if (!scheduleData || !scheduleData.shows || scheduleData.shows.length === 0) {
      console.log('No schedule data available, using fallback');
      // Create HTML for the fallback TV schedule
      let scheduleHTML = `
        <div class="tv-schedule">
          <div class="next-show">
            <div class="show-time">22:00</div>
            <div class="show-title">AKTUELLT MED VÄDER</div>
          </div>
        </div>
      `;

      // Update the bottom container
      bottomContainer.innerHTML = scheduleHTML;
      return;
    }

    // Find the next show based on current time
    const nextShow = findNextShow(scheduleData.shows);

    if (!nextShow) {
      console.log('Could not determine next show');
      return;
    }

    // Create HTML for the TV schedule
    let scheduleHTML = `
      <div class="tv-schedule">
        <div class="next-show">
          <div class="show-time">${nextShow.time}</div>
          <div class="show-title">${nextShow.title.toUpperCase()}</div>
        </div>
      </div>
    `;

    // Update the bottom container
    bottomContainer.innerHTML = scheduleHTML;
  } catch (error) {
    console.error('Error updating TV schedule:', error);
    bottomContainer.innerHTML = 'Fel vid hämtning av programinformation';
  }
};

// Initialize the clock
const initClock = (containerId) => {
  const container = document.getElementById(containerId);
  const clockElement = container.querySelector('.clock');
  const hourHandGroup = container.querySelector('.hour-hand-group');
  const minuteHandGroup = container.querySelector('.minute-hand-group');
  const secondHandGroup = container.querySelector('.second-hand-group');

  const updateClockSize = () => {
    // Get the dimensions of the clock container
    const clockContainer = container.querySelector('.clock-container');
    const containerRect = clockContainer.getBoundingClientRect();

    // Use the smaller of width or height to ensure the clock fits within the container
    // and leave some padding (90% of the smaller dimension)
    let size = Math.min(containerRect.width, containerRect.height) * 0.9;

    // Ensure we have a minimum size
    size = Math.max(size, 100);

    // Set equal width and height to maintain perfect circle aspect ratio
    Object.assign(clockElement.style, {
      width: `${size}px`,
      height: `${size}px`,
      minWidth: `${size}px`, // Prevent flex shrinking
      minHeight: `${size}px` // Prevent flex shrinking
    });
  };

  let prevSecond = new Date().getSeconds();
  let prevMinute = new Date().getMinutes();
  let secondRotations = 0;

  const updateClock = () => {
    const now = new Date();
    const hours = now.getHours() % 12;
    const minutes = now.getMinutes();
    const seconds = now.getSeconds();

    // Check if the minute has changed
    if (prevMinute !== minutes) {
      // Update the TV schedule when the minute changes
      updateTVSchedule();
      prevMinute = minutes;
    }

    if (prevSecond === 59 && seconds === 0) {
      secondRotations++;
    }
    prevSecond = seconds;

    const hourAngle = hours * 30 + minutes / 2;
    const minuteAngle = minutes * 6 + seconds / 10;
    const secondAngle = secondRotations * 360 + seconds * 6;

    hourHandGroup.style.transform = `rotate(${hourAngle}deg)`;
    minuteHandGroup.style.transform = `rotate(${minuteAngle}deg)`;
    secondHandGroup.style.transform = `rotate(${secondAngle}deg)`;
  };

  const instantUpdateClock = () => {
    secondHandGroup.classList.add('no-transition');
    updateClock();
    // Re-enable transition on next frame
    requestAnimationFrame(() => {
      secondHandGroup.classList.remove('no-transition');
    });
  };

  updateClockSize();
  window.addEventListener('resize', updateClockSize);

  instantUpdateClock();

  // Sync interval with real clock
  const timeUntilNextSecond = 1000 - new Date().getMilliseconds();
  setTimeout(() => {
    instantUpdateClock();
    setInterval(updateClock, 1000);
  }, timeUntilNextSecond);
};

// Initialize theme switch button
const initThemeSwitch = () => {
  const themeButton = document.getElementById('theme-switch');
  if (!themeButton) return;

  themeButton.addEventListener('click', () => {
    // Toggle between available clock types
    const types = Object.keys(CLOCK_TYPES);
    const currentIndex = types.indexOf(currentClockType);
    const nextIndex = (currentIndex + 1) % types.length;
    const nextType = types[nextIndex];

    setClockType(nextType);
  });

  // Update button text
  const updateButtonText = () => {
    const theme = CLOCK_TYPES[currentClockType];
    themeButton.textContent = theme.name;
  };

  updateButtonText();

  // Listen for hash changes (back/forward navigation)
  window.addEventListener('hashchange', () => {
    const newType = getCurrentClockType();
    if (newType !== currentClockType) {
      currentClockType = newType;
      applyTheme(newType);
      updateClockAssets(newType);
      updateTVSchedule();
      updateButtonText();
    }
  });
};

document.addEventListener('DOMContentLoaded', () => {
  // Initialize clock type from URL
  currentClockType = getCurrentClockType();

  // Apply initial theme
  applyTheme(currentClockType);

  // Initialize components
  initClock('root');
  initThemeSwitch();

  // Initial update of TV schedule
  updateTVSchedule();
});
