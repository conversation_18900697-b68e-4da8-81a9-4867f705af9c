:root {
  --primary-color: #085E20;
  --text-color: white;
  --clock-color: white;
  --hand-color: #085E20;
}

@font-face {
  font-family: 'futura-now-text';
  src: url('/fonts/futura-now-text.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  background-color: var(--primary-color);
}

body {
  margin: 0;
  padding: 0;
  font-family: Arial, Helvetica, sans-serif;
  display: flex;
  justify-content: center;
}

#root {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  overflow: hidden;
}

.app-container {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
  padding: 20px;

  /* Always maintain 4:3 aspect ratio */
  aspect-ratio: 4/3;

  /* Responsive sizing based on viewport dimensions */
  width: min(100vw, calc(100vh * 4 / 3));
  height: min(100vh, calc(100vw * 3 / 4));

  /* Prevent shrinking */
  flex-shrink: 0;
}

/* Main content area (top 2/3) */
.main-content {
  flex: 2; /* Takes 2/3 of the height */
  width: 100%;
  display: flex;
  flex-direction: row; /* Side by side layout */
  overflow: hidden;
}

/* Left container (1/3 of width) */
.left-container {
  flex: 1; /* Takes 1/3 of the width */
  height: 100%;
  display: flex;
  justify-content: left;
  align-items: end;
  color: var(--text-color);
  font-size: 2rem;
  padding-left: 40px;
  padding-bottom: 50px;
  box-sizing: border-box;
}

/* Footer */
.footer {
  position: absolute;
  bottom: 10px;
  right: 20px;
  z-index: 10;
}

/* Theme switch button */
.theme-switch-button {
  background-color: transparent;
  border: 2px solid var(--text-color);
  color: var(--text-color);
  font-family: 'futura-now-text', Arial, Helvetica, sans-serif;
  font-size: 0.9rem;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.8;
}

.theme-switch-button:hover {
  background-color: var(--text-color);
  color: var(--primary-color);
  opacity: 1;
}

.theme-switch-button:active {
  transform: scale(0.95);
}

/* Clock container (2/3 of width) */
.clock-container {
  flex: 2; /* Takes 2/3 of the width */
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0px;
  box-sizing: border-box;
}

/* Bottom area (1/3 of height) */
.bottom-container {
  flex: 1; /* Takes 1/3 of the height */
  width: 100%;
  display: flex;
  justify-content: left;
  align-items: start;
  color: var(--text-color);
  padding-left: 40px;
  box-sizing: border-box;
}

.next-show {
  font-family: 'futura-now-text', Arial, Helvetica, sans-serif;
  font-size: 3vw;
  font-weight: bold;
}

/* TV Schedule Styles */
.tv-schedule {
  width: 100%;
}

.clock {
  position: relative;
  background-color: var(--clock-color);
  border-radius: 50%;
  aspect-ratio: 1 / 1; /* Force 1:1 aspect ratio */
  overflow: hidden; /* Ensure content doesn't overflow the circle */
}

.hour-hand-group,
.minute-hand-group,
.second-hand-group,
.hour-markers-group {
  color: var(--hand-color);
  transform-origin: center;
}

.second-hand-group {
  transition: transform 0.1s cubic-bezier(.95,.4,.53,1.48);
}

.second-hand-group.no-transition {
  transition: none !important;
}
