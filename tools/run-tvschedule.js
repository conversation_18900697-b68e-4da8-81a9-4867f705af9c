#!/usr/bin/env node

import { onRequest } from '../functions/tvschedule.js';

async function main() {
  // Create a mock context similar to what Cloudflare Pages provides
  const mockContext = {
    request: new Request('http://localhost:8787/'),
  };

  try {
    const response = await onRequest(mockContext);
    const data = await response.json();
    console.log(JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

main();