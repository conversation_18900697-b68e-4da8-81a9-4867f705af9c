<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>TV2 Klocka</title>
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <link rel="manifest" href="/manifest.json">
    <link rel="apple-touch-icon" type="image/png" href="/icons/icon-192.png">
    <link rel="icon" type="image/png" href="/icons/icon-16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="/icons/icon-32.png" sizes="32x32">
    <link rel="icon" type="image/png" href="/icons/icon-192.png" sizes="192x192">
  </head>
  <body>
    <div id="root">
      <div class="app-container">
        <!-- Main content area (top 2/3) -->
        <div class="main-content">
          <!-- Left div (1/3 of width) -->
          <div class="left-container">
            <img src="/TV2.svg" width="70%" />
            <button id="theme-switch" class="theme-switch-button">SVT2 1980</button>
          </div>

          <!-- Clock taking 2/3 of width -->
          <div class="clock-container">
            <div class="clock">
              <svg viewBox="0 0 200 200" width="100%" height="100%" preserveAspectRatio="xMidYMid meet">
                <g class="hour-markers-group">
                  <use xlink:href="/src/assets/hour-markers.svg#hour-markers"></use>
                </g>
                <g class="hour-hand-group">
                  <use xlink:href="/src/assets/hour-hand.svg#hour-hand"></use>
                </g>
                <g class="minute-hand-group">
                  <use xlink:href="/src/assets/minute-hand.svg#minute-hand"></use>
                </g>
                <g class="second-hand-group">
                  <use xlink:href="/src/assets/second-hand.svg#second-hand"></use>
                </g>
              </svg>
            </div>
          </div>
        </div>

        <!-- Bottom area (1/3 of height) -->
        <div class="bottom-container">
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
