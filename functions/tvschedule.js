/**
 * Cloudflare Pages Function to scrape TV schedule for SVT2 from tv-tabla.se
 *
 * @param {Request} request - The incoming request
 * @param {Object} env - Environment variables
 * @param {Object} ctx - Context object
 * @returns {Response} JSON response with SVT2 schedule
 */
export async function onRequest(context) {
  try {
    // Set up CORS headers
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Content-Type': 'application/json'
    };

    // Handle OPTIONS request for CORS preflight
    if (context.request.method === 'OPTIONS') {
      return new Response(null, { headers });
    }

    // Fetch the TV schedule page
    const response = await fetch('https://tv-tabla.se/');
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
    }

    const html = await response.text();

    // Extract SVT2 schedule data
    const svt2Schedule = extractSVT2Schedule(html);

    // Return the schedule as JSON
    return new Response(JSON.stringify(svt2Schedule, null, 2), {
      headers
    });
  } catch (error) {
    // Handle errors
    return new Response(JSON.stringify({
      error: 'Failed to fetch TV schedule',
      message: error.message
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }
}

/**
 * Extract SVT2 schedule from the HTML content
 *
 * @param {string} html - HTML content from tv-tabla.se
 * @returns {Object} Structured SVT2 schedule data
 */
export function extractSVT2Schedule(html) {
  // Initialize result object
  const result = {
    shows: []
  };

  try {
    // First, find the SVT2 section
    const svt2HeaderIndex = html.indexOf('id="svt2-svt-se"');
    if (svt2HeaderIndex === -1) {
      return {
        ...result,
        error: 'Could not find SVT2 section on the page'
      };
    }

    // Find the list that contains the SVT2 programs
    const svt2Section = html.substring(svt2HeaderIndex, svt2HeaderIndex + 10000);

    // Extract all program links for SVT2
    const programRegex = /<a href="\/tabla\/svt2\/p\/([^"/]+)\/(\d+)\/"[^>]*><time>(\d+:\d+)\s*<\/time>([^<]+)<\/a>/gi;
    let programMatch;

    while ((programMatch = programRegex.exec(svt2Section)) !== null) {
      const dateTime = programMatch[2];
      const time = programMatch[3].trim();
      const title = programMatch[4].trim().toUpperCase();

      // Only add if we have at least a time and title
      if (dateTime && time && title) {
        result.shows.push({
          time,
          title,
          dateTime
        });
      }
    }

    // Add additional metadata
    result.lastUpdated = new Date().toISOString();
    result.totalShows = result.shows.length;

    return result;
  } catch (error) {
    return {
      ...result,
      error: 'Error parsing SVT2 schedule',
      message: error.message
    };
  }
}
