import { JSD<PERSON> } from 'jsdom';

// Channel configuration
const CHANNEL_CONFIG = {
  'svt2': {
    name: 'SVT2',
    selector: '#svt2-svt-se',
    url: 'https://tv-tabla.se/'
  },
  'svt1': {
    name: 'SVT1', 
    selector: '#svt1-svt-se',
    url: 'https://tv-tabla.se/'
  }
};

// Function to extract schedule data for a specific channel
const extractChannelSchedule = (html, channelConfig) => {
  const dom = new JSDOM(html);
  const document = dom.window.document;

  // Find the channel container
  const channelContainer = document.querySelector(channelConfig.selector);
  
  if (!channelContainer) {
    console.log(`Channel container not found for selector: ${channelConfig.selector}`);
    return { shows: [], totalShows: 0, channel: channelConfig.name };
  }

  // Extract show information
  const showLinks = channelContainer.querySelectorAll('a[href*="/tabla/"]');
  const shows = [];

  showLinks.forEach(link => {
    const timeElement = link.querySelector('time');
    const href = link.getAttribute('href');
    
    if (timeElement && href) {
      const time = timeElement.textContent.trim();
      const title = link.textContent.replace(time, '').trim();
      
      // Extract dateTime from href (e.g., /tabla/svt2/p/show/202505181900/)
      const dateTimeMatch = href.match(/\/(\d{12})\//);
      const dateTime = dateTimeMatch ? dateTimeMatch[1] : null;
      
      if (time && title && dateTime) {
        shows.push({
          time,
          title,
          dateTime
        });
      }
    }
  });

  console.log(`Extracted ${shows.length} shows for ${channelConfig.name}`);
  return {
    shows,
    totalShows: shows.length,
    channel: channelConfig.name
  };
};

export async function onRequest(context) {
  const { params } = context;
  const channel = params.channel;

  // CORS headers
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
  };

  // Handle OPTIONS request for CORS
  if (context.request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers });
  }

  // Validate channel parameter
  if (!channel || !CHANNEL_CONFIG[channel]) {
    return new Response(JSON.stringify({
      error: 'Invalid channel',
      message: `Channel '${channel}' not supported. Available channels: ${Object.keys(CHANNEL_CONFIG).join(', ')}`
    }), {
      status: 400,
      headers
    });
  }

  try {
    const channelConfig = CHANNEL_CONFIG[channel];
    console.log(`Fetching TV schedule for ${channelConfig.name}...`);

    // Fetch the TV schedule page
    const response = await fetch(channelConfig.url);
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
    }

    const html = await response.text();

    // Extract channel schedule data
    const channelSchedule = extractChannelSchedule(html, channelConfig);

    // Return the schedule as JSON
    return new Response(JSON.stringify(channelSchedule, null, 2), {
      headers
    });
  } catch (error) {
    // Handle errors
    console.error(`Error fetching TV schedule for ${channel}:`, error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch TV schedule',
      message: error.message,
      channel: channel
    }), {
      status: 500,
      headers
    });
  }
}
