// Channel configuration
const CHANNEL_CONFIG = {
  'svt2': {
    name: 'SVT2',
    sectionId: 'svt2-svt-se',
    url: 'https://tv-tabla.se/'
  },
  'svt1': {
    name: 'SVT1',
    sectionId: 'svt1-svt-se',
    url: 'https://tv-tabla.se/'
  }
};

/**
 * Extract schedule data for a specific channel using regex parsing
 * (same approach as the original tvschedule.js)
 *
 * @param {string} html - HTML content from tv-tabla.se
 * @param {Object} channelConfig - Channel configuration
 * @returns {Object} Structured schedule data
 */
function extractChannelSchedule(html, channelConfig) {
  // Initialize result object
  const result = {
    shows: [],
    channel: channelConfig.name
  };

  try {
    // Find the channel section
    const sectionHeaderIndex = html.indexOf(`id="${channelConfig.sectionId}"`);
    if (sectionHeaderIndex === -1) {
      console.log(`Channel section not found for: ${channelConfig.sectionId}`);
      return {
        ...result,
        totalShows: 0
      };
    }

    // Extract a reasonable section of HTML around the channel
    const channelSection = html.substring(sectionHeaderIndex, sectionHeaderIndex + 10000);

    // Create regex pattern for the specific channel
    const channelName = channelConfig.sectionId.includes('svt2') ? 'svt2' : 'svt1';
    const programRegex = new RegExp(`<a href="\\/tabla\\/${channelName}\\/p\\/([^"/]+)\\/(\\d+)\\/"[^>]*><time>(\\d+:\\d+)\\s*<\\/time>([^<]+)<\\/a>`, 'gi');

    let programMatch;

    while ((programMatch = programRegex.exec(channelSection)) !== null) {
      const dateTime = programMatch[2];
      const time = programMatch[3].trim();
      const title = programMatch[4].trim().toUpperCase();

      // Only add if we have at least a time and title
      if (dateTime && time && title) {
        result.shows.push({
          time,
          title,
          dateTime
        });
      }
    }

    // Add metadata
    result.totalShows = result.shows.length;
    console.log(`Extracted ${result.totalShows} shows for ${channelConfig.name}`);

    return result;
  } catch (error) {
    console.error(`Error parsing ${channelConfig.name} schedule:`, error);
    return {
      ...result,
      totalShows: 0,
      error: 'Error parsing schedule',
      message: error.message
    };
  }
}

export async function onRequest(context) {
  const { params } = context;
  const channel = params.channel;

  // CORS headers
  const headers = {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
  };

  // Handle OPTIONS request for CORS
  if (context.request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers });
  }

  // Validate channel parameter
  if (!channel || !CHANNEL_CONFIG[channel]) {
    return new Response(JSON.stringify({
      error: 'Invalid channel',
      message: `Channel '${channel}' not supported. Available channels: ${Object.keys(CHANNEL_CONFIG).join(', ')}`
    }), {
      status: 400,
      headers
    });
  }

  try {
    const channelConfig = CHANNEL_CONFIG[channel];
    console.log(`Fetching TV schedule for ${channelConfig.name}...`);

    // Fetch the TV schedule page
    const response = await fetch(channelConfig.url);
    if (!response.ok) {
      throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
    }

    const html = await response.text();

    // Extract channel schedule data
    const channelSchedule = extractChannelSchedule(html, channelConfig);

    // Return the schedule as JSON
    return new Response(JSON.stringify(channelSchedule, null, 2), {
      headers
    });
  } catch (error) {
    // Handle errors
    console.error(`Error fetching TV schedule for ${channel}:`, error);
    return new Response(JSON.stringify({
      error: 'Failed to fetch TV schedule',
      message: error.message,
      channel: channel
    }), {
      status: 500,
      headers
    });
  }
}
